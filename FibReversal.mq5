//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//+------------------------------------------------------------------+
#property version     "1.0"
#property description "Fibonacci Reversal Strategy with fractal detection and Fibonacci retracement levels"

#define FibReversalMagic 9765427

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters
input double InpPriceChangePercentage = 3;          // Price change threshold percentage
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // Hard stop percentage
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,    // Fibonacci detection phase
   PHASE_ACTIVE_LONG, // Active phase searching for long entries
   PHASE_ACTIVE_SHORT // Active phase searching for short entries
};

//+------------------------------------------------------------------+
//| Fibonacci Reversal Expert Advisor class                         |
//+------------------------------------------------------------------+
class CFibReversalExpert
  {
protected:
   // Trading objects
   double            m_adjusted_point;             // Point value adjusted for 3 or 5 digits
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Indicator handles
   int               m_handle_fractals;            // Fractals indicator handle

   // Forward-looking fractal detection variables
   datetime          m_lastProcessedBarTime;       // Time of last processed 4H bar
   bool              m_waitingForNewBar;           // Flag to wait for new 4H bar after position exit

   // Trading variables
   double            m_priceSens;                  // Price sensitivity
   ulong             m_longTicket;                 // Long position ticket
   ulong             m_shortTicket;                // Short position ticket
   double            m_longEntryPrice;             // Long position entry price
   double            m_shortEntryPrice;            // Short position entry price

   // Python script variables (exact match)
   string            m_mode;                       // 'Yükseliş Fibi Arıyor' or 'Düşüş Fibi Arıyor'
   bool              m_adayFibExists;              // aday_fib exists
   bool              m_aktifFibExists;             // aktif_fib exists

   // Locked peak/trough (kilitli_tepe_fiyat, kilitli_dip_fiyat)
   double            m_kilitliTepeFiyat;           // Locked peak price
   datetime          m_kilitliTepeZaman;           // Locked peak time
   double            m_kilitliDipFiyat;            // Locked trough price
   datetime          m_kilitliDipZaman;            // Locked trough time

   // Fibonacci candidate (aday_fib)
   double            m_adayTepeFiyat;              // Candidate peak price
   datetime          m_adayTepeZaman;              // Candidate peak time
   double            m_adayDipFiyat;               // Candidate trough price
   datetime          m_adayDipZaman;               // Candidate trough time
   double            m_adayLevel236;               // Candidate 236 level
   double            m_adayLevel500;               // Candidate 500 level
   string            m_adayType;                   // 'Yükseliş' or 'Düşüş'

   // Active fibonacci (aktif_fib)
   double            m_aktifTepeFiyat;             // Active peak price
   datetime          m_aktifTepeZaman;             // Active peak time
   double            m_aktifDipFiyat;              // Active trough price
   datetime          m_aktifDipZaman;              // Active trough time
   double            m_aktifLevel236;              // Active 236 level
   double            m_aktifLevel500;              // Active 500 level
   string            m_aktifType;                  // 'Yükseliş' or 'Düşüş'

   // Heartbeat logging variables
   datetime          m_lastHeartbeatTime;          // Last heartbeat timestamp
   int               m_heartbeatInterval;          // Heartbeat interval in seconds (1 hour)

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   bool              Init(void);
   bool              Processing(void);
   ENUM_TRADING_PHASE GetTradingPhase(void) { return m_aktifFibExists ? PHASE_ACTIVE_LONG : PHASE_PASSIVE; }

protected:
   bool              InitCheckParameters(const int digits_adjust);
   bool              InitIndicators(void);
   bool              ProcessPassivePhase(void);
   bool              ProcessActivePhase(void);

   // Python script logic functions
   bool              ProcessPythonScriptLogic(void);
   bool              ProcessPythonFibonacciLogic(datetime currentTime, double currentHigh, double currentLow, double currentClose);
   bool              ProcessBullishFibonacciSearch(datetime currentTime, double currentHigh, double currentLow, double currentClose);
   bool              ProcessBearishFibonacciSearch(datetime currentTime, double currentHigh, double currentLow, double currentClose);
   bool              ProcessCandidateFibonacci(datetime currentTime, double currentHigh, double currentLow, double currentClose);
   bool              ProcessActiveTrade(datetime currentTime, double currentHigh, double currentLow, double currentClose);
   void              CreateCandidateFibonacci(double tepeFiyat, datetime tepeZaman, double dipFiyat, datetime dipZaman, string fibType);
   void              ActivateFibonacci(void);

   // Legacy functions (to be removed)
   bool              DetectFractals(void);
   bool              UpdatePeakTroughTracking(void);
   void              CalculateFibonacciLevels(void);

   // Trading functions
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   void              OpenLongPosition(void);
   void              OpenShortPosition(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);
   void              LogHeartbeat(void);
  };
//--- global expert
CFibReversalExpert ExtExpert;
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_adjusted_point(0),
                                               m_handle_fractals(INVALID_HANDLE),
                                               m_priceSens(0),
                                               m_longTicket(-1),
                                               m_shortTicket(-1),
                                               m_longEntryPrice(-1),
                                               m_shortEntryPrice(-1),
                                               m_lastProcessedBarTime(0),
                                               m_waitingForNewBar(false),
                                               m_mode("Yükseliş Fibi Arıyor"),
                                               m_adayFibExists(false),
                                               m_aktifFibExists(false),
                                               m_kilitliTepeFiyat(0),
                                               m_kilitliTepeZaman(0),
                                               m_kilitliDipFiyat(DBL_MAX),
                                               m_kilitliDipZaman(0),
                                               m_adayTepeFiyat(0),
                                               m_adayTepeZaman(0),
                                               m_adayDipFiyat(0),
                                               m_adayDipZaman(0),
                                               m_adayLevel236(0),
                                               m_adayLevel500(0),
                                               m_adayType(""),
                                               m_aktifTepeFiyat(0),
                                               m_aktifTepeZaman(0),
                                               m_aktifDipFiyat(0),
                                               m_aktifDipZaman(0),
                                               m_aktifLevel236(0),
                                               m_aktifLevel500(0),
                                               m_aktifType(""),
                                               m_lastHeartbeatTime(0),
                                               m_heartbeatInterval(3600)
  {
   // No array initialization needed for forward-looking approach
  }
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
  {
  }
//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
  {
   // Initialize trading objects
   m_symbol.Name(Symbol());
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   m_priceSens = MathPow(10, -1 * m_symbol.Digits());

   // Adjust point value for 3 or 5 digit brokers
   int digits_adjust = 1;
   if(m_symbol.Digits() == 3 || m_symbol.Digits() == 5)
      digits_adjust = 10;
   m_adjusted_point = m_symbol.Point() * digits_adjust;

   // Set trading deviation
   m_trade.SetDeviationInPoints(3 * digits_adjust);

   // Initialize strategy state
   ResetStrategy();

//---
   if(!InitCheckParameters(digits_adjust))
      return(false);
   if(!InitIndicators())
      return(false);

   // Log initial heartbeat on EA startup
   printf("=== EA INITIALIZED === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("EA: Fibonacci Reversal Strategy started successfully");
   printf("Parameters: Risk=%.1f%%, Price Change=%.1f%%, Fib Level=%.3f, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpFibonacciLevel, InpHardStopPercentage);
   printf("Heartbeat logging enabled - will report status every hour");

//--- succeed
   return(true);
  }
//+------------------------------------------------------------------+
//| Checking for input parameters                                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(const int digits_adjust)
  {
   // Validate risk percentage (only when not using fixed lot size)
   if(!InpUseFixedLotSize && (InpRiskPercent <= 0 || InpRiskPercent > 100))
     {
      printf("Risk percentage must be between 0 and 100, current value: %f", InpRiskPercent);
      return(false);
     }

   // Validate fixed lot size (only when using fixed lot size)
   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
     {
      printf("Fixed lot size must be greater than 0, current value: %f", InpFixedLotSize);
      return(false);
     }

   // Validate price change percentage
   if(InpPriceChangePercentage <= 0)
     {
      printf("Price change percentage must be greater than 0, current value: %f", InpPriceChangePercentage);
      return(false);
     }

   // No lookback period validation needed for forward-looking approach

   // Validate Fibonacci level
   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
     {
      printf("Fibonacci level must be between 0 and 1, current value: %f", InpFibonacciLevel);
      return(false);
     }

   // Validate hard stop percentage
   if(InpHardStopPercentage <= 0)
     {
      printf("Hard stop percentage must be greater than 0, current value: %f", InpHardStopPercentage);
      return(false);
     }

   // Validate fixed lot size against symbol specifications
   if(InpUseFixedLotSize)
     {
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      if(InpFixedLotSize < minLot)
        {
         printf("Fixed lot size (%f) is below minimum allowed (%f)", InpFixedLotSize, minLot);
         return(false);
        }

      if(InpFixedLotSize > maxLot)
        {
         printf("Fixed lot size (%f) is above maximum allowed (%f)", InpFixedLotSize, maxLot);
         return(false);
        }

      printf("Fixed lot size validation: Input=%f, Min=%f, Max=%f, Step=%f", InpFixedLotSize, minLot, maxLot, lotStep);
     }

   return(true);
  }
//+------------------------------------------------------------------+
//| Initialization of the indicators                                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitIndicators(void)
  {
   // Create Fractals indicator on 4H timeframe
   if(m_handle_fractals == INVALID_HANDLE)
      if((m_handle_fractals = iFractals(NULL, PERIOD_H4)) == INVALID_HANDLE)
      {
         printf("Error creating Fractals indicator on 4H timeframe");
         return(false);
      }

   printf("DEBUG: Fractals indicator created successfully on 4H timeframe, handle: %d", m_handle_fractals);

   // Wait for indicator to calculate initial values
   Sleep(1000);

   return(true);
  }
//+------------------------------------------------------------------+
//| Reset strategy to passive phase (Python style)                 |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   // Reset to initial Python script state
   m_mode = "Yükseliş Fibi Arıyor";
   m_adayFibExists = false;
   m_aktifFibExists = false;

   m_kilitliTepeFiyat = 0;
   m_kilitliTepeZaman = 0;
   m_kilitliDipFiyat = DBL_MAX;
   m_kilitliDipZaman = 0;

   m_adayTepeFiyat = 0;
   m_adayTepeZaman = 0;
   m_adayDipFiyat = 0;
   m_adayDipZaman = 0;
   m_adayLevel236 = 0;
   m_adayLevel500 = 0;
   m_adayType = "";

   m_aktifTepeFiyat = 0;
   m_aktifTepeZaman = 0;
   m_aktifDipFiyat = 0;
   m_aktifDipZaman = 0;
   m_aktifLevel236 = 0;
   m_aktifLevel500 = 0;
   m_aktifType = "";

   m_waitingForNewBar = true;
   printf("Strategy reset to Python script initial state - mode: %s", m_mode);
}

//+------------------------------------------------------------------+
//| Check for long position closing (Python style)                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   bool res = false;

   if(!m_position.SelectByTicket(m_longTicket))
      return false;

   // Get just-completed bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Just-completed bar high
   double currentLow = iLow(NULL, PERIOD_H4, 0);   // Just-completed bar low

   // Python exit conditions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   bool tpHit = (currentHigh >= m_aktifLevel500);

   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   double hardStopLevel = m_aktifLevel236 * (1.0 - InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentLow < hardStopLevel);

   if(tpHit || hardStopHit)
   {
      // Calculate profit percentage (Python style)
      double profitPercent = 0;
      if(tpHit)
      {
         profitPercent = (m_aktifLevel500 - m_aktifLevel236) / m_aktifLevel236 * 100.0;
         printf("Closing long position: TP hit - High %.5f >= Fib 500 %.5f, Profit: %.2f%%",
                currentHigh, m_aktifLevel500, profitPercent);
      }
      if(hardStopHit)
      {
         profitPercent = -InpHardStopPercentage;
         printf("Closing long position: Hard stop hit - Low %.5f < Stop %.5f, Loss: %.2f%%",
                currentLow, hardStopLevel, profitPercent);
      }

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Long position closed successfully - %s", tpHit ? "Fib Completed" : "Hard Stop");
         m_longTicket = -1;
         m_longEntryPrice = -1;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing long position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Check for short position closing (Python style)                |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   bool res = false;

   if(!m_position.SelectByTicket(m_shortTicket))
      return false;

   // Get just-completed bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Just-completed bar high
   double currentLow = iLow(NULL, PERIOD_H4, 0);   // Just-completed bar low

   // Python exit conditions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   bool tpHit = (currentLow <= m_aktifLevel500);

   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   double hardStopLevel = m_aktifLevel236 * (1.0 + InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentHigh > hardStopLevel);

   if(tpHit || hardStopHit)
   {
      // Calculate profit percentage (Python style)
      double profitPercent = 0;
      if(tpHit)
      {
         profitPercent = (m_aktifLevel236 - m_aktifLevel500) / m_aktifLevel236 * 100.0;
         printf("Closing short position: TP hit - Low %.5f <= Fib 500 %.5f, Profit: %.2f%%",
                currentLow, m_aktifLevel500, profitPercent);
      }
      if(hardStopHit)
      {
         profitPercent = -InpHardStopPercentage;
         printf("Closing short position: Hard stop hit - High %.5f > Stop %.5f, Loss: %.2f%%",
                currentHigh, hardStopLevel, profitPercent);
      }

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Short position closed successfully - %s", tpHit ? "Fib Completed" : "Hard Stop");
         m_shortTicket = -1;
         m_shortEntryPrice = -1;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing short position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage or fixed lot size    |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(double stopLossDistance)
{
   double lotSize = 0;

   // Use fixed lot size if enabled
   if(InpUseFixedLotSize)
   {
      // Get symbol specifications for normalization
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Normalize the fixed lot size to valid step
      lotSize = MathRound(InpFixedLotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

      // Log if normalization occurred
      if(MathAbs(lotSize - InpFixedLotSize) > 0.0001)
      {
         printf("Fixed lot size normalized from %f to %f (step: %f)", InpFixedLotSize, lotSize, lotStep);
      }
   }
   else
   {
      // Calculate lot size based on risk percentage
      // Get account balance
      double accountBalance = m_account.Balance();

      // Calculate risk amount in account currency
      double riskAmount = accountBalance * (InpRiskPercent / 100.0);

      // Get symbol specifications
      double tickValue = m_symbol.TickValue();
      double tickSize = m_symbol.TickSize();

      if(tickSize > 0 && tickValue > 0)
      {
         // Calculate value per point
         double valuePerPoint = (tickValue / tickSize);

         // Calculate lot size based on risk
         if(stopLossDistance > 0)
            lotSize = riskAmount / (stopLossDistance * valuePerPoint);
      }

      // Normalize lot size to symbol specifications
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Round to lot step
      lotSize = MathRound(lotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   }

   return lotSize;
}

//+------------------------------------------------------------------+
//| Process passive phase - Python-style pattern detection         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPassivePhase(void)
{
   // Check if we're waiting for a new 4H bar
   if(m_waitingForNewBar)
   {
      datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
      if(currentBarTime != m_lastProcessedBarTime)
      {
         // New bar has formed, we can start detection
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - ready to start Python script logic");
      }
      else
      {
         // Still waiting for new bar
         return false;
      }
   }

   // Python script main logic - process current bar
   return ProcessPythonScriptLogic();
}

//+------------------------------------------------------------------+
//| Process Python script logic exactly                             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPythonScriptLogic(void)
{
   // Get last 3 bars for pattern detection (Python: close0, close1, close2)
   double close0 = iClose(NULL, PERIOD_H4, 2); // i-2 in Python (oldest)
   double close1 = iClose(NULL, PERIOD_H4, 1); // i-1 in Python (middle)
   double close2 = iClose(NULL, PERIOD_H4, 0); // i in Python (current/newest)

   datetime currentTime = iTime(NULL, PERIOD_H4, 0);
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   double currentClose = iClose(NULL, PERIOD_H4, 0);

   // DEBUG: Log the 3-bar pattern values
   printf("DEBUG: 3-bar pattern - close0=%.5f, close1=%.5f, close2=%.5f at %s",
          close0, close1, close2, TimeToString(currentTime));

   // FIRST: Process existing fibonacci logic (activation/reset) BEFORE mode switching
   bool fibonacciProcessed = ProcessPythonFibonacciLogic(currentTime, currentHigh, currentLow, currentClose);

   // SECOND: Only check for mode switching if no fibonacci was activated
   if(!fibonacciProcessed)
   {
      // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor':
      if(close0 > close1 && close1 > close2 && m_mode != "Yükseliş Fibi Arıyor")
      {
         m_mode = "Yükseliş Fibi Arıyor";

         // Clear any existing candidate fibonacci when switching modes
         m_adayFibExists = false;

         // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
         m_kilitliTepeFiyat = MathMax(MathMax(iHigh(NULL, PERIOD_H4, 2), iHigh(NULL, PERIOD_H4, 1)), currentHigh);
         // Find the time of the max high
         if(iHigh(NULL, PERIOD_H4, 2) == m_kilitliTepeFiyat)
            m_kilitliTepeZaman = iTime(NULL, PERIOD_H4, 2);
         else if(iHigh(NULL, PERIOD_H4, 1) == m_kilitliTepeFiyat)
            m_kilitliTepeZaman = iTime(NULL, PERIOD_H4, 1);
         else
            m_kilitliTepeZaman = currentTime;

         // Reset trough tracking for bullish search
         m_kilitliDipFiyat = DBL_MAX;
         m_kilitliDipZaman = 0;

         printf("PYTHON: Mode changed to 'Yükseliş Fibi Arıyor', locked peak: %.5f at %s",
                m_kilitliTepeFiyat, TimeToString(m_kilitliTepeZaman));
      }
      // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor':
      else if(close0 < close1 && close1 < close2 && m_mode != "Düşüş Fibi Arıyor")
      {
         m_mode = "Düşüş Fibi Arıyor";

         // Clear any existing candidate fibonacci when switching modes
         m_adayFibExists = false;

         // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
         m_kilitliDipFiyat = MathMin(MathMin(iLow(NULL, PERIOD_H4, 2), iLow(NULL, PERIOD_H4, 1)), currentLow);
         // Find the time of the min low
         if(iLow(NULL, PERIOD_H4, 2) == m_kilitliDipFiyat)
            m_kilitliDipZaman = iTime(NULL, PERIOD_H4, 2);
         else if(iLow(NULL, PERIOD_H4, 1) == m_kilitliDipFiyat)
            m_kilitliDipZaman = iTime(NULL, PERIOD_H4, 1);
         else
            m_kilitliDipZaman = currentTime;

         // Reset peak tracking for bearish search
         m_kilitliTepeFiyat = 0;
         m_kilitliTepeZaman = 0;

         printf("PYTHON: Mode changed to 'Düşüş Fibi Arıyor', locked trough: %.5f at %s",
                m_kilitliDipFiyat, TimeToString(m_kilitliDipZaman));
      }
   }

   return fibonacciProcessed;
}

//+------------------------------------------------------------------+
//| Process Python fibonacci logic exactly                          |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPythonFibonacciLogic(datetime currentTime, double currentHigh, double currentLow, double currentClose)
{
   // Python: if aktif_fib: (check active trade first)
   if(m_aktifFibExists)
   {
      return ProcessActiveTrade(currentTime, currentHigh, currentLow, currentClose);
   }

   // Python: if aday_fib: (check candidate fibonacci)
   if(m_adayFibExists)
   {
      return ProcessCandidateFibonacci(currentTime, currentHigh, currentLow, currentClose);
   }

   // Python: Main fibonacci detection logic
   if(m_mode == "Yükseliş Fibi Arıyor")
   {
      return ProcessBullishFibonacciSearch(currentTime, currentHigh, currentLow, currentClose);
   }
   else if(m_mode == "Düşüş Fibi Arıyor")
   {
      return ProcessBearishFibonacciSearch(currentTime, currentHigh, currentLow, currentClose);
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process bullish fibonacci search (Python lines 105-115)        |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessBullishFibonacciSearch(datetime currentTime, double currentHigh, double currentLow, double currentClose)
{
   // Python: if row['High'] > kilitli_tepe_fiyat:
   if(currentHigh > m_kilitliTepeFiyat)
   {
      // Python: kilitli_tepe_fiyat, kilitli_tepe_zaman = row['High'], current_time
      m_kilitliTepeFiyat = currentHigh;
      m_kilitliTepeZaman = currentTime;
      printf("PYTHON: Peak updated to %.5f at %s", m_kilitliTepeFiyat, TimeToString(m_kilitliTepeZaman));
   }
   // Python: elif kilitli_tepe_zaman:
   else if(m_kilitliTepeZaman > 0)
   {
      // Track minimum low since peak was locked (like Python script)
      if(m_kilitliDipFiyat == DBL_MAX || currentLow < m_kilitliDipFiyat)
      {
         m_kilitliDipFiyat = currentLow;
         m_kilitliDipZaman = currentTime;
         printf("PYTHON: Trough updated to %.5f at %s (bullish search)", m_kilitliDipFiyat, TimeToString(m_kilitliDipZaman));
      }

      // Python: fark = (kilitli_tepe_fiyat - minimum_low) / minimum_low
      double fark = (m_kilitliTepeFiyat - m_kilitliDipFiyat) / m_kilitliDipFiyat;

      // Python: if fark >= MIN_YUZDE_FARK:
      if(fark >= 0.03) // MIN_YUZDE_FARK = 0.03
      {
         // Python: aday_fib = FibonacciRetracement(kilitli_tepe_fiyat, kilitli_tepe_zaman, minimum_low, minimum_low_time, 'Yükseliş')
         CreateCandidateFibonacci(m_kilitliTepeFiyat, m_kilitliTepeZaman, m_kilitliDipFiyat, m_kilitliDipZaman, "Yükseliş");
         printf("PYTHON: Bullish candidate fibonacci created - Peak: %.5f, Trough: %.5f, Range: %.2f%%",
                m_kilitliTepeFiyat, m_kilitliDipFiyat, fark * 100);
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Create candidate fibonacci (Python FibonacciRetracement class)  |
//+------------------------------------------------------------------+
void CFibReversalExpert::CreateCandidateFibonacci(double tepeFiyat, datetime tepeZaman, double dipFiyat, datetime dipZaman, string fibType)
{
   m_adayFibExists = true;
   m_adayTepeFiyat = tepeFiyat;
   m_adayTepeZaman = tepeZaman;
   m_adayDipFiyat = dipFiyat;
   m_adayDipZaman = dipZaman;
   m_adayType = fibType;

   // Python fibonacci calculation
   double diff = MathAbs(m_adayTepeFiyat - m_adayDipFiyat);
   if(m_adayType == "Yükseliş")
   {
      // Python: self.level_236 = self.dip_fiyat + 0.236 * diff
      m_adayLevel236 = m_adayDipFiyat + 0.236 * diff;
      // Python: self.level_500 = self.dip_fiyat + 0.5 * diff
      m_adayLevel500 = m_adayDipFiyat + 0.5 * diff;
   }
   else
   {
      // Python: self.level_236 = self.tepe_fiyat - 0.236 * diff
      m_adayLevel236 = m_adayTepeFiyat - 0.236 * diff;
      // Python: self.level_500 = self.tepe_fiyat - 0.5 * diff
      m_adayLevel500 = m_adayTepeFiyat - 0.5 * diff;
   }

   printf("PYTHON: Candidate fibonacci created - Type: %s, 236: %.5f, 500: %.5f",
          m_adayType, m_adayLevel236, m_adayLevel500);
}

//+------------------------------------------------------------------+
//| Process candidate fibonacci (Python lines 90-103)              |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessCandidateFibonacci(datetime currentTime, double currentHigh, double currentLow, double currentClose)
{
   if(m_adayType == "Yükseliş")
   {
      // Python: if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat:
      if(currentClose < m_adayDipFiyat || currentClose > m_adayTepeFiyat)
      {
         // Python: aday_fib = None
         m_adayFibExists = false;
         printf("PYTHON: Bullish candidate fibonacci reset - close %.5f outside range [%.5f - %.5f]",
                currentClose, m_adayDipFiyat, m_adayTepeFiyat);
         return false;
      }
      // Python: elif row['Close'] > aday_fib.level_236:
      else if(currentClose > m_adayLevel236)
      {
         // Python: aktif_fib = aday_fib; aday_fib = None
         ActivateFibonacci();
         printf("PYTHON: Bullish fibonacci ACTIVATED - close %.5f > 236 level %.5f",
                currentClose, m_adayLevel236);
         return true;
      }
   }
   else // "Düşüş"
   {
      // Python: if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat:
      if(currentClose > m_adayTepeFiyat || currentClose < m_adayDipFiyat)
      {
         // Python: aday_fib = None
         m_adayFibExists = false;
         printf("PYTHON: Bearish candidate fibonacci reset - close %.5f outside range [%.5f - %.5f]",
                currentClose, m_adayDipFiyat, m_adayTepeFiyat);
         return false;
      }
      // Python: elif row['Close'] < aday_fib.level_236:
      else if(currentClose < m_adayLevel236)
      {
         // Python: aktif_fib = aday_fib; aday_fib = None
         ActivateFibonacci();
         printf("PYTHON: Bearish fibonacci ACTIVATED - close %.5f < 236 level %.5f",
                currentClose, m_adayLevel236);
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Activate fibonacci (Python: aktif_fib = aday_fib)              |
//+------------------------------------------------------------------+
void CFibReversalExpert::ActivateFibonacci(void)
{
   // Copy candidate to active
   m_aktifFibExists = true;
   m_aktifTepeFiyat = m_adayTepeFiyat;
   m_aktifTepeZaman = m_adayTepeZaman;
   m_aktifDipFiyat = m_adayDipFiyat;
   m_aktifDipZaman = m_adayDipZaman;
   m_aktifLevel236 = m_adayLevel236;
   m_aktifLevel500 = m_adayLevel500;
   m_aktifType = m_adayType;

   // Clear candidate
   m_adayFibExists = false;

   // Open trade based on type
   if(m_aktifType == "Yükseliş")
   {
      OpenLongPosition();
   }
   else
   {
      OpenShortPosition();
   }
}

//+------------------------------------------------------------------+
//| Process bearish fibonacci search (Python lines similar to bullish) |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessBearishFibonacciSearch(datetime currentTime, double currentHigh, double currentLow, double currentClose)
{
   // Python: if row['Low'] < kilitli_dip_fiyat:
   if(currentLow < m_kilitliDipFiyat)
   {
      // Python: kilitli_dip_fiyat, kilitli_dip_zaman = row['Low'], current_time
      m_kilitliDipFiyat = currentLow;
      m_kilitliDipZaman = currentTime;
      printf("PYTHON: Trough updated to %.5f at %s", m_kilitliDipFiyat, TimeToString(m_kilitliDipZaman));
   }
   // Python: elif kilitli_dip_zaman:
   else if(m_kilitliDipZaman > 0)
   {
      // Track maximum high since trough was locked (like Python script)
      if(m_kilitliTepeFiyat == 0 || currentHigh > m_kilitliTepeFiyat)
      {
         m_kilitliTepeFiyat = currentHigh;
         m_kilitliTepeZaman = currentTime;
         printf("PYTHON: Peak updated to %.5f at %s (bearish search)", m_kilitliTepeFiyat, TimeToString(m_kilitliTepeZaman));
      }

      // Python: fark = (maximum_high - kilitli_dip_fiyat) / kilitli_dip_fiyat
      double fark = (m_kilitliTepeFiyat - m_kilitliDipFiyat) / m_kilitliDipFiyat;

      // Python: if fark >= MIN_YUZDE_FARK:
      if(fark >= 0.03) // MIN_YUZDE_FARK = 0.03
      {
         // Python: aday_fib = FibonacciRetracement(maximum_high, maximum_high_time, kilitli_dip_fiyat, kilitli_dip_zaman, 'Düşüş')
         CreateCandidateFibonacci(m_kilitliTepeFiyat, m_kilitliTepeZaman, m_kilitliDipFiyat, m_kilitliDipZaman, "Düşüş");
         printf("PYTHON: Bearish candidate fibonacci created - Peak: %.5f, Trough: %.5f, Range: %.2f%%",
                m_kilitliTepeFiyat, m_kilitliDipFiyat, fark * 100);
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process active trade (Python aktif_fib logic)                  |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActiveTrade(datetime currentTime, double currentHigh, double currentLow, double currentClose)
{
   // For now, just maintain the active trade
   // The actual trade management will be handled by existing position management
   // This function exists to match Python script structure

   // Check if position still exists
   if(!m_position.Select(m_symbol.Name()))
   {
      // Position closed, reset active fibonacci
      m_aktifFibExists = false;
      printf("PYTHON: Active trade closed, resetting active fibonacci");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Open long position (Python style)                              |
//+------------------------------------------------------------------+
void CFibReversalExpert::OpenLongPosition(void)
{
   double ask = m_symbol.Ask();
   double stopLoss = m_aktifDipFiyat; // Stop at fibonacci trough
   double takeProfit = m_aktifLevel500; // Take profit at 50% level

   double lotSize = CalculateLotSize(MathAbs(ask - stopLoss));

   if(m_trade.Buy(lotSize, m_symbol.Name(), ask, stopLoss, takeProfit, "Python Bullish Fib"))
   {
      printf("PYTHON: Long position opened - Entry: %.5f, SL: %.5f, TP: %.5f, Lot: %.2f",
             ask, stopLoss, takeProfit, lotSize);
   }
   else
   {
      printf("PYTHON: Failed to open long position - Error: %d", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Open short position (Python style)                             |
//+------------------------------------------------------------------+
void CFibReversalExpert::OpenShortPosition(void)
{
   double bid = m_symbol.Bid();
   double stopLoss = m_aktifTepeFiyat; // Stop at fibonacci peak
   double takeProfit = m_aktifLevel500; // Take profit at 50% level

   double lotSize = CalculateLotSize(MathAbs(stopLoss - bid));

   if(m_trade.Sell(lotSize, m_symbol.Name(), bid, stopLoss, takeProfit, "Python Bearish Fib"))
   {
      printf("PYTHON: Short position opened - Entry: %.5f, SL: %.5f, TP: %.5f, Lot: %.2f",
             bid, stopLoss, takeProfit, lotSize);
   }
   else
   {
      printf("PYTHON: Failed to open short position - Error: %d", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Process active phase - Python-style peak/trough tracking       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(!m_aktifFibExists)
      return false;

   // Check for position closure
   if(!m_position.Select(m_symbol.Name()))
   {
      m_aktifFibExists = false;
      printf("Position closed - returning to passive phase");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Legacy function - now handled by ProcessPythonScriptLogic      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{
   // This function is now obsolete - pattern detection is handled by ProcessPythonScriptLogic
   return false;
}
//+------------------------------------------------------------------+
//| Legacy function - now handled by CreateCandidateFibonacci      |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   // This function is now obsolete - fibonacci calculation is handled by CreateCandidateFibonacci
}

//+------------------------------------------------------------------+
//| Legacy function - now handled by ProcessPythonScriptLogic      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdatePeakTroughTracking(void)
{
   // This function is now obsolete - peak/trough tracking is handled by ProcessPythonScriptLogic
   return false;
}




//+------------------------------------------------------------------+
//| Open long position (Python style - immediate activation)       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   // This function is now obsolete - trade opening is handled by OpenLongPosition
   return false;
}
//+------------------------------------------------------------------+
//| Open short position (Python style - immediate activation)      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   // This function is now obsolete - trade opening is handled by OpenShortPosition
   return false;
}
//+------------------------------------------------------------------+
//| Log heartbeat with EA status and position information           |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   // Update heartbeat timestamp
   m_lastHeartbeatTime = TimeCurrent();

   // Get account information
   double balance = m_account.Balance();
   double equity = m_account.Equity();
   double freeMargin = m_account.FreeMargin();

   // Count positions for this symbol
   int posCount = 0;
   string posInfo = "";

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         m_position.SelectByIndex(i);
         posCount++;

         string posType = (m_position.PositionType() == POSITION_TYPE_BUY) ? "LONG" : "SHORT";
         double openPrice = m_position.PriceOpen();
         double lotSize = m_position.Volume();
         double profit = m_position.Profit();
         datetime openTime = m_position.Time();

         // Calculate SL/TP based on strategy
         double sl = 0;
         double tp = 0;

         if(m_position.PositionType() == POSITION_TYPE_BUY)
         {
            sl = openPrice * (1.0 - InpHardStopPercentage / 100.0);
            tp = m_aktifLevel500;
         }
         else
         {
            sl = openPrice * (1.0 + InpHardStopPercentage / 100.0);
            tp = m_aktifLevel500;
         }

         // Calculate position duration
         int durationHours = (int)((TimeCurrent() - openTime) / 3600);
         int durationMins = (int)(((TimeCurrent() - openTime) % 3600) / 60);

         posInfo += StringFormat("%s: %.2f lots @ %.5f, P&L: %.2f, SL: %.5f, TP: %.5f, Duration: %dh%dm | ",
                                posType, lotSize, openPrice, profit, sl, tp, durationHours, durationMins);
      }
   }

   // Log heartbeat information
   printf("=== HEARTBEAT === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("Account: Balance=%.2f, Equity=%.2f, Free Margin=%.2f", balance, equity, freeMargin);
   printf("Positions: %d active | %s", posCount, (posCount > 0) ? posInfo : "No positions");

   // Log strategy status
   string phaseStr = m_aktifFibExists ? "ACTIVE" : "PASSIVE";

   if(!m_aktifFibExists)
   {
      // In passive phase
      if(m_waitingForNewBar)
         printf("Strategy: Phase=%s, Mode=%s, Waiting for new 4H bar", phaseStr, m_mode);
      else
         printf("Strategy: Phase=%s, Mode=%s, Looking for patterns", phaseStr, m_mode);
   }
   else
   {
      // In active phase, show the current Fibonacci range
      printf("Strategy: Phase=%s, Type=%s, Peak=%.5f, Trough=%.5f, Fib236=%.5f, Fib500=%.5f",
             phaseStr, m_aktifType, m_aktifTepeFiyat, m_aktifDipFiyat, m_aktifLevel236, m_aktifLevel500);
   }

   printf("Market: Bid=%.5f, Ask=%.5f, Spread=%.1f points",
          m_symbol.Bid(), m_symbol.Ask(), m_symbol.Spread());
   printf("EA Status: Risk=%.1f%%, Price Change=%.1f%%, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpHardStopPercentage);
   printf("=== END HEARTBEAT ===");
}






//+------------------------------------------------------------------+
//| main function returns true if any position processed             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
  {
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return(false);

   // Debug: Track processing calls
   static int processingCounter = 0;
   processingCounter++;
   if(processingCounter % 10000 == 0) // Every 10000 calls
   {
      printf("DEBUG: Processing called %d times, Current phase: %s, Mode: %s",
             processingCounter,
             m_aktifFibExists ? "ACTIVE" : "PASSIVE",
             m_mode);
   }

   // Check for heartbeat logging (once per hour)
   datetime currentTime = TimeCurrent();
   if(m_lastHeartbeatTime == 0 || (currentTime - m_lastHeartbeatTime) >= m_heartbeatInterval)
   {
      LogHeartbeat();
   }

   // Count and identify positions for this symbol
   uint posNumber = 0;
   int posTotal = PositionsTotal();
   for(int i = 0; i < posTotal; i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         posNumber++;
         m_position.SelectByIndex(i);
         if(m_position.PositionType() == POSITION_TYPE_BUY)
            m_longTicket = m_position.Ticket();
         else if(m_position.PositionType() == POSITION_TYPE_SELL)
            m_shortTicket = m_position.Ticket();
      }
   }

   // Process positions based on count
   if(posNumber > 1)
   {
      Alert("Error: Multiple positions detected");
      return(false);
   }
   else if(posNumber == 1)
   {
      // Handle existing positions - check for closing conditions
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(LongClosed())
            return(true);
      }

      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(ShortClosed())
            return(true);
      }
   }
   else if(posNumber == 0)
   {
      // No positions - process strategy phases
      if(!m_aktifFibExists)
      {
         if(ProcessPassivePhase())
            return(true);
      }
      else
      {
         if(ProcessActivePhase())
            return(true);
      }
   }

   return(false);
  }
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
  {
//--- create all necessary objects
   if(!ExtExpert.Init())
      return(INIT_FAILED);
//--- succeed
   return(INIT_SUCCEEDED);
  }






//+------------------------------------------------------------------+
//| Expert tick handler (Python style - process every new 4H bar)  |
//+------------------------------------------------------------------+
void OnTick(void)
{
   // Process on every new 4H bar (Python style)
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);

   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      ExtExpert.Processing();
   }
}
